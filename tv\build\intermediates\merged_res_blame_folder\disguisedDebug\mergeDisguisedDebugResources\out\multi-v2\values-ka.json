{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeDisguisedDebugResources-75:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd2de9df1fcb7d47d0f664953b17c729\\transformed\\foundation-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "123,124", "startColumns": "4,4", "startOffsets": "9770,9858", "endColumns": "87,90", "endOffsets": "9853,9944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\55d105b608835fb0a5975933fd0070b6\\transformed\\core-1.13.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "46,47,48,49,50,51,52,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3426,3522,3624,3723,3822,3928,4032,9399", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3517,3619,3718,3817,3923,4027,4145,9495"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c98e1a41ddbd0f58bbf4b7505ad9616c\\transformed\\appcompat-1.7.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "723,831,934,1045,1131,1236,1349,1432,1511,1602,1695,1790,1884,1984,2077,2172,2267,2358,2449,2530,2643,2749,2847,2960,3065,3169,3327,9014", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "826,929,1040,1126,1231,1344,1427,1506,1597,1690,1785,1879,1979,2072,2167,2262,2353,2444,2525,2638,2744,2842,2955,3060,3164,3322,3421,9091"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5a79ec61513eb95e1266e907126a2152\\transformed\\media3-ui-1.4.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,290,483,673,757,841,920,1018,1120,1205,1270,1369,1468,1533,1598,1662,1729,1857,1986,2113,2188,2267,2341,2426,2522,2618,2685,2751,2804,2865,2913,2974,3040,3119,3183,3251,3315,3376,3442,3494,3556,3632,3708,3761", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,83,83,78,97,101,84,64,98,98,64,64,63,66,127,128,126,74,78,73,84,95,95,66,65,52,60,47,60,65,78,63,67,63,60,65,51,61,75,75,52,64", "endOffsets": "285,478,668,752,836,915,1013,1115,1200,1265,1364,1463,1528,1593,1657,1724,1852,1981,2108,2183,2262,2336,2421,2517,2613,2680,2746,2799,2860,2908,2969,3035,3114,3178,3246,3310,3371,3437,3489,3551,3627,3703,3756,3821"}, "to": {"startLines": "2,11,15,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,340,533,4624,4708,4792,4871,4969,5071,5156,5221,5320,5419,5484,5549,5613,5680,5808,5937,6064,6139,6218,6292,6377,6473,6569,6636,7406,7459,7520,7568,7629,7695,7774,7838,7906,7970,8031,8097,8149,8211,8287,8363,8416", "endLines": "10,14,18,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,83,83,78,97,101,84,64,98,98,64,64,63,66,127,128,126,74,78,73,84,95,95,66,65,52,60,47,60,65,78,63,67,63,60,65,51,61,75,75,52,64", "endOffsets": "335,528,718,4703,4787,4866,4964,5066,5151,5216,5315,5414,5479,5544,5608,5675,5803,5932,6059,6134,6213,6287,6372,6468,6564,6631,6697,7454,7515,7563,7624,7690,7769,7833,7901,7965,8026,8092,8144,8206,8282,8358,8411,8476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f0ce41bc1ab5a7ccfdd795c249b2c5b2\\transformed\\ui-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,386,489,579,659,755,845,932,1021,1112,1184,1262,1340,1415,1494,1564", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,77,77,74,78,69,120", "endOffsets": "196,282,381,484,574,654,750,840,927,1016,1107,1179,1257,1335,1410,1489,1559,1680"}, "to": {"startLines": "53,54,55,56,57,108,109,110,111,112,113,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4150,4246,4332,4431,4534,8481,8561,8657,8747,8834,8923,9096,9168,9246,9324,9500,9579,9649", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,77,77,74,78,69,120", "endOffsets": "4241,4327,4426,4529,4619,8556,8652,8742,8829,8918,9009,9163,9241,9319,9394,9574,9644,9765"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9db5af2f2458547656275ea3d5385dab\\transformed\\media3-exoplayer-1.4.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,192,270,343,424,499,587,674", "endColumns": "71,64,77,72,80,74,87,86,84", "endOffsets": "122,187,265,338,419,494,582,669,754"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6702,6774,6839,6917,6990,7071,7146,7234,7321", "endColumns": "71,64,77,72,80,74,87,86,84", "endOffsets": "6769,6834,6912,6985,7066,7141,7229,7316,7401"}}]}]}