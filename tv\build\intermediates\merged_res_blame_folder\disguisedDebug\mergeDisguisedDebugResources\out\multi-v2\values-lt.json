{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeDisguisedDebugResources-75:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9db5af2f2458547656275ea3d5385dab\\transformed\\media3-exoplayer-1.4.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,200,267,335,416,490,587,682", "endColumns": "80,63,66,67,80,73,96,94,74", "endOffsets": "131,195,262,330,411,485,582,677,752"}, "to": {"startLines": "86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6953,7034,7098,7165,7233,7314,7388,7485,7580", "endColumns": "80,63,66,67,80,73,96,94,74", "endOffsets": "7029,7093,7160,7228,7309,7383,7480,7575,7650"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\55d105b608835fb0a5975933fd0070b6\\transformed\\core-1.13.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "50,51,52,53,54,55,56,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3752,3850,3960,4059,4162,4273,4383,9645", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "3845,3955,4054,4157,4268,4378,4498,9741"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c98e1a41ddbd0f58bbf4b7505ad9616c\\transformed\\appcompat-1.7.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "983,1099,1203,1316,1403,1505,1627,1710,1790,1884,1980,2077,2173,2276,2372,2470,2566,2660,2754,2837,2946,3054,3154,3264,3369,3475,3651,9257", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "1094,1198,1311,1398,1500,1622,1705,1785,1879,1975,2072,2168,2271,2367,2465,2561,2655,2749,2832,2941,3049,3149,3259,3364,3470,3646,3747,9336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd2de9df1fcb7d47d0f664953b17c729\\transformed\\foundation-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,87", "endOffsets": "138,226"}, "to": {"startLines": "127,128", "startColumns": "4,4", "startOffsets": "10031,10119", "endColumns": "87,87", "endOffsets": "10114,10202"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5a79ec61513eb95e1266e907126a2152\\transformed\\media3-ui-1.4.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,298,622,933,1017,1100,1178,1275,1372,1446,1510,1606,1702,1773,1838,1901,1974,2082,2192,2300,2372,2448,2521,2595,2684,2772,2841,2908,2961,3019,3074,3135,3201,3270,3335,3403,3467,3525,3598,3657,3720,3797,3874,3930", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,83,82,77,96,96,73,63,95,95,70,64,62,72,107,109,107,71,75,72,73,88,87,68,66,52,57,54,60,65,68,64,67,63,57,72,58,62,76,76,55,66", "endOffsets": "293,617,928,1012,1095,1173,1270,1367,1441,1505,1601,1697,1768,1833,1896,1969,2077,2187,2295,2367,2443,2516,2590,2679,2767,2836,2903,2956,3014,3069,3130,3196,3265,3330,3398,3462,3520,3593,3652,3715,3792,3869,3925,3992"}, "to": {"startLines": "2,11,17,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,348,672,4978,5062,5145,5223,5320,5417,5491,5555,5651,5747,5818,5883,5946,6019,6127,6237,6345,6417,6493,6566,6640,6729,6817,6886,7655,7708,7766,7821,7882,7948,8017,8082,8150,8214,8272,8345,8404,8467,8544,8621,8677", "endLines": "10,16,22,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "endColumns": "17,12,12,83,82,77,96,96,73,63,95,95,70,64,62,72,107,109,107,71,75,72,73,88,87,68,66,52,57,54,60,65,68,64,67,63,57,72,58,62,76,76,55,66", "endOffsets": "343,667,978,5057,5140,5218,5315,5412,5486,5550,5646,5742,5813,5878,5941,6014,6122,6232,6340,6412,6488,6561,6635,6724,6812,6881,6948,7703,7761,7816,7877,7943,8012,8077,8145,8209,8267,8340,8399,8462,8539,8616,8672,8739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f0ce41bc1ab5a7ccfdd795c249b2c5b2\\transformed\\ui-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,1005,1093,1168,1245,1322,1397,1477,1560", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,76,76,74,79,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,1000,1088,1163,1240,1317,1392,1472,1555,1677"}, "to": {"startLines": "57,58,59,60,61,112,113,114,115,116,117,119,120,121,122,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4503,4596,4680,4778,4883,8744,8821,8912,8999,9083,9169,9341,9416,9493,9570,9746,9826,9909", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,76,76,74,79,82,121", "endOffsets": "4591,4675,4773,4878,4973,8816,8907,8994,9078,9164,9252,9411,9488,9565,9640,9821,9904,10026"}}]}]}