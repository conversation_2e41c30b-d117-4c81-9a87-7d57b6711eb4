{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeDisguisedDebugResources-75:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd2de9df1fcb7d47d0f664953b17c729\\transformed\\foundation-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,86", "endOffsets": "138,225"}, "to": {"startLines": "123,124", "startColumns": "4,4", "startOffsets": "9663,9751", "endColumns": "87,86", "endOffsets": "9746,9833"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\55d105b608835fb0a5975933fd0070b6\\transformed\\core-1.13.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "46,47,48,49,50,51,52,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3390,3487,3589,3688,3788,3891,4004,9305", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "3482,3584,3683,3783,3886,3999,4115,9401"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c98e1a41ddbd0f58bbf4b7505ad9616c\\transformed\\appcompat-1.7.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "697,804,906,1015,1099,1202,1321,1399,1475,1566,1659,1754,1848,1948,2041,2136,2230,2321,2412,2496,2600,2708,2809,2914,3029,3134,3291,8919", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "799,901,1010,1094,1197,1316,1394,1470,1561,1654,1749,1843,1943,2036,2131,2225,2316,2407,2491,2595,2703,2804,2909,3024,3129,3286,3385,8999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9db5af2f2458547656275ea3d5385dab\\transformed\\media3-exoplayer-1.4.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,191,259,328,419,489,579,667", "endColumns": "72,62,67,68,90,69,89,87,79", "endOffsets": "123,186,254,323,414,484,574,662,742"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6620,6693,6756,6824,6893,6984,7054,7144,7232", "endColumns": "72,62,67,68,90,69,89,87,79", "endOffsets": "6688,6751,6819,6888,6979,7049,7139,7227,7307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f0ce41bc1ab5a7ccfdd795c249b2c5b2\\transformed\\ui-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,393,493,585,670,763,857,938,1028,1119,1191,1267,1344,1420,1497,1563", "endColumns": "95,85,105,99,91,84,92,93,80,89,90,71,75,76,75,76,65,113", "endOffsets": "196,282,388,488,580,665,758,852,933,1023,1114,1186,1262,1339,1415,1492,1558,1672"}, "to": {"startLines": "53,54,55,56,57,108,109,110,111,112,113,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4120,4216,4302,4408,4508,8385,8470,8563,8657,8738,8828,9004,9076,9152,9229,9406,9483,9549", "endColumns": "95,85,105,99,91,84,92,93,80,89,90,71,75,76,75,76,65,113", "endOffsets": "4211,4297,4403,4503,4595,8465,8558,8652,8733,8823,8914,9071,9147,9224,9300,9478,9544,9658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5a79ec61513eb95e1266e907126a2152\\transformed\\media3-ui-1.4.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,290,473,647,723,798,870,973,1074,1153,1221,1320,1421,1489,1552,1615,1683,1813,1933,2060,2128,2206,2276,2361,2446,2530,2593,2667,2720,2781,2831,2892,2954,3020,3084,3149,3210,3269,3338,3396,3456,3530,3604,3667", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,75,74,71,102,100,78,67,98,100,67,62,62,67,129,119,126,67,77,69,84,84,83,62,73,52,60,49,60,61,65,63,64,60,58,68,57,59,73,73,62,72", "endOffsets": "285,468,642,718,793,865,968,1069,1148,1216,1315,1416,1484,1547,1610,1678,1808,1928,2055,2123,2201,2271,2356,2441,2525,2588,2662,2715,2776,2826,2887,2949,3015,3079,3144,3205,3264,3333,3391,3451,3525,3599,3662,3735"}, "to": {"startLines": "2,11,15,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,340,523,4600,4676,4751,4823,4926,5027,5106,5174,5273,5374,5442,5505,5568,5636,5766,5886,6013,6081,6159,6229,6314,6399,6483,6546,7312,7365,7426,7476,7537,7599,7665,7729,7794,7855,7914,7983,8041,8101,8175,8249,8312", "endLines": "10,14,18,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,75,74,71,102,100,78,67,98,100,67,62,62,67,129,119,126,67,77,69,84,84,83,62,73,52,60,49,60,61,65,63,64,60,58,68,57,59,73,73,62,72", "endOffsets": "335,518,692,4671,4746,4818,4921,5022,5101,5169,5268,5369,5437,5500,5563,5631,5761,5881,6008,6076,6154,6224,6309,6394,6478,6541,6615,7360,7421,7471,7532,7594,7660,7724,7789,7850,7909,7978,8036,8096,8170,8244,8307,8380"}}]}]}