#Mon Aug 18 16:34:42 HKT 2025
base.0=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeExtDexDisguisedDebug\\classes.dex
base.1=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeLibDexDisguisedDebug\\10\\classes.dex
base.10=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeLibDexDisguisedDebug\\7\\classes.dex
base.11=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeLibDexDisguisedDebug\\9\\classes.dex
base.12=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeProjectDexDisguisedDebug\\0\\classes.dex
base.13=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeProjectDexDisguisedDebug\\10\\classes.dex
base.14=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeProjectDexDisguisedDebug\\11\\classes.dex
base.15=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeProjectDexDisguisedDebug\\12\\classes.dex
base.16=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeProjectDexDisguisedDebug\\13\\classes.dex
base.17=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeProjectDexDisguisedDebug\\14\\classes.dex
base.18=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeProjectDexDisguisedDebug\\15\\classes.dex
base.19=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeProjectDexDisguisedDebug\\1\\classes.dex
base.2=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeLibDexDisguisedDebug\\12\\classes.dex
base.20=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeProjectDexDisguisedDebug\\2\\classes.dex
base.21=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeProjectDexDisguisedDebug\\3\\classes.dex
base.22=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeProjectDexDisguisedDebug\\4\\classes.dex
base.23=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeProjectDexDisguisedDebug\\5\\classes.dex
base.24=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeProjectDexDisguisedDebug\\6\\classes.dex
base.25=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeProjectDexDisguisedDebug\\7\\classes.dex
base.26=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeProjectDexDisguisedDebug\\8\\classes.dex
base.27=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeProjectDexDisguisedDebug\\9\\classes.dex
base.28=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\desugar_lib_dex\\disguisedDebug\\l8DexDesugarLibDisguisedDebug\\classes1000.dex
base.29=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeExtDexDisguisedDebug\\classes2.dex
base.3=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeLibDexDisguisedDebug\\14\\classes.dex
base.30=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeExtDexDisguisedDebug\\classes3.dex
base.31=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeExtDexDisguisedDebug\\classes4.dex
base.4=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeLibDexDisguisedDebug\\1\\classes.dex
base.5=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeLibDexDisguisedDebug\\2\\classes.dex
base.6=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeLibDexDisguisedDebug\\3\\classes.dex
base.7=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeLibDexDisguisedDebug\\4\\classes.dex
base.8=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeLibDexDisguisedDebug\\5\\classes.dex
base.9=C\:\\Users\\fly\\StudioProjects\\mytv-android\\tv\\build\\intermediates\\dex\\disguisedDebug\\mergeLibDexDisguisedDebug\\6\\classes.dex
path.0=classes.dex
path.1=10/classes.dex
path.10=7/classes.dex
path.11=9/classes.dex
path.12=0/classes.dex
path.13=10/classes.dex
path.14=11/classes.dex
path.15=12/classes.dex
path.16=13/classes.dex
path.17=14/classes.dex
path.18=15/classes.dex
path.19=1/classes.dex
path.2=12/classes.dex
path.20=2/classes.dex
path.21=3/classes.dex
path.22=4/classes.dex
path.23=5/classes.dex
path.24=6/classes.dex
path.25=7/classes.dex
path.26=8/classes.dex
path.27=9/classes.dex
path.28=classes1000.dex
path.29=classes2.dex
path.3=14/classes.dex
path.30=classes3.dex
path.31=classes4.dex
path.4=1/classes.dex
path.5=2/classes.dex
path.6=3/classes.dex
path.7=4/classes.dex
path.8=5/classes.dex
path.9=6/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.10=classes11.dex
renamed.11=classes12.dex
renamed.12=classes13.dex
renamed.13=classes14.dex
renamed.14=classes15.dex
renamed.15=classes16.dex
renamed.16=classes17.dex
renamed.17=classes18.dex
renamed.18=classes19.dex
renamed.19=classes20.dex
renamed.2=classes3.dex
renamed.20=classes21.dex
renamed.21=classes22.dex
renamed.22=classes23.dex
renamed.23=classes24.dex
renamed.24=classes25.dex
renamed.25=classes26.dex
renamed.26=classes27.dex
renamed.27=classes28.dex
renamed.28=classes29.dex
renamed.29=classes30.dex
renamed.3=classes4.dex
renamed.30=classes31.dex
renamed.31=classes32.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
renamed.9=classes10.dex
