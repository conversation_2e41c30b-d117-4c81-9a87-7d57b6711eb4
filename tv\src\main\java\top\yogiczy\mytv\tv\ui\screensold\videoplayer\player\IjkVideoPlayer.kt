package top.yogiczy.mytv.tv.ui.screensold.videoplayer.player

import android.content.Context
import android.graphics.SurfaceTexture
import android.view.Surface
import android.view.SurfaceView
import android.view.TextureView
import android.view.TextureView.SurfaceTextureListener
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import top.yogiczy.mytv.core.data.entities.channel.ChannelLine
import top.yogiczy.mytv.core.util.utils.toHeaders
import top.yogiczy.mytv.tv.ui.utils.Configs
import tv.danmaku.ijk.media.player.IMediaPlayer
import tv.danmaku.ijk.media.player.IjkMediaMeta
import tv.danmaku.ijk.media.player.IjkMediaPlayer


class IjkVideoPlayer(
    private val context: Context,
    private val coroutineScope: CoroutineScope,
) : VideoPlayer(coroutineScope),
    IMediaPlayer.OnPreparedListener,
    IMediaPlayer.OnVideoSizeChangedListener,
    IMediaPlayer.OnErrorListener {

    private val player by lazy {
        IjkMediaPlayer().apply {
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "dns_cache_clear", 1)
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "dns_cache_timeout", 0)
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "http-detect-range-support", 0)
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "reconnect", 1)
            setOption(
                IjkMediaPlayer.OPT_CATEGORY_FORMAT,
                "timeout",
                Configs.videoPlayerLoadTimeout
            )
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "analyzemaxduration", 100L)
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "analyzeduration", 1)
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "probesize", 1024 * 10)
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "fflags", "fastseek")

            // RTSP 特定配置
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "rtsp_transport", "udp")
            // setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "rtsp_flags", "prefer_tcp")
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "stimeout", 20000000L) // 20秒超时
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "max_delay", 500000L) // 最大延迟500ms
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "buffer_size", 1024 * 1024) // 1MB缓冲区
            setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "infbuf", 1) // 无限缓冲
        }
    }
    private var cacheSurfaceView: SurfaceView? = null
    private var cacheSurfaceTexture: Surface? = null
    private var updateJob: Job? = null
    private lateinit var currentChannelLine: ChannelLine

    private fun setOption() {
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec", 1)
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec-all-videos", 1)
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec-hevc", 1)
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "opensles", 0)
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "framedrop", 1) // 减少丢帧，避免画面卡住
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "start-on-prepared", 1)
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "enable-accurate-seek", 1)

        // 针对RTSP流的播放器配置
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "packet-buffering", 0) // 禁用包缓冲
        // player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "sync", "ext") // 外部同步
        // player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max_cached_duration", 3000L) // 最大缓存3秒
        // player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "min-frames", 2) // 最小帧数
        // player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max-fps", 0) // 不限制帧率
    }

    override fun prepare(line: ChannelLine) {
        currentChannelLine = line
        player.reset()

        // 针对RTSP流的特殊处理
        if (line.playableUrl.startsWith("rtsp://", ignoreCase = true)) {
            // RTSP流不需要HTTP headers
            player.setDataSource(line.playableUrl)
        } else {
            player.setDataSource(
                line.playableUrl,
                Configs.videoPlayerHeaders.toHeaders() + mapOf(
                    "User-Agent" to (line.httpUserAgent ?: Configs.videoPlayerUserAgent),
                )
            )
        }

        setOption()
        player.prepareAsync()

        triggerPrepared()
    }

    override fun play() {
        player.start()
    }

    override fun pause() {
        player.pause()
    }

    override fun seekTo(position: Long) {
        player.seekTo(position)
    }

    override fun setVolume(volume: Float) {
    }

    override fun getVolume(): Float {
        return 1f
    }

    override fun stop() {
        player.stop()
        updateJob?.cancel()
        super.stop()
    }

    override fun selectVideoTrack(track: Metadata.Video?) {}

    override fun selectAudioTrack(track: Metadata.Audio?) {}

    override fun selectSubtitleTrack(track: Metadata.Subtitle?) {}

    override fun setVideoSurfaceView(surfaceView: SurfaceView) {
        cacheSurfaceView = surfaceView
        cacheSurfaceTexture?.release()
        cacheSurfaceTexture = null
    }

    override fun setVideoTextureView(textureView: TextureView) {
        cacheSurfaceView = null
        textureView.surfaceTextureListener = object : SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(
                surfaceTexture: SurfaceTexture,
                width: Int,
                height: Int
            ) {
                cacheSurfaceTexture = Surface(surfaceTexture)
                player.setSurface(cacheSurfaceTexture)
            }

            override fun onSurfaceTextureSizeChanged(
                surfaceTexture: SurfaceTexture,
                width: Int,
                height: Int
            ) {
            }

            override fun onSurfaceTextureDestroyed(surfaceTexture: SurfaceTexture): Boolean {
                return true
            }

            override fun onSurfaceTextureUpdated(surfaceTexture: SurfaceTexture) {
            }
        }
    }

    override fun initialize() {
        super.initialize()
        player.setOnPreparedListener(this)
        player.setOnVideoSizeChangedListener(this)
        player.setOnErrorListener(this)
    }

    override fun release() {
        player.setOnPreparedListener(null)
        player.setOnVideoSizeChangedListener(null)
        player.setOnErrorListener(null)
        player.stop()
        player.release()
        cacheSurfaceTexture?.release()
        super.release()
    }

    override fun onPrepared(player: IMediaPlayer) {
        cacheSurfaceView?.let { player.setDisplay(it.holder) }
        cacheSurfaceTexture?.let { player.setSurface(it) }

        // 对于RTSP流，确保立即开始播放
        if (currentChannelLine.playableUrl.startsWith("rtsp://", ignoreCase = true)) {
            player.start()
        }

        val info = player.mediaInfo
        metadata = Metadata(
            video = Metadata.Video(
                width = info.mMeta.mVideoStream?.mWidth,
                height = info.mMeta.mVideoStream?.mHeight,
                frameRate = info.mMeta.mVideoStream?.mFpsNum?.toFloat(),
                bitrate = info.mMeta.mVideoStream?.mBitrate?.toInt(),
                mimeType = info.mMeta.mVideoStream?.mCodecName,
                decoder = info.mVideoDecoderImpl,
            ),

            audio = Metadata.Audio(
                channels = when (info.mMeta.mAudioStream?.mChannelLayout) {
                    IjkMediaMeta.AV_CH_LAYOUT_MONO -> 1
                    IjkMediaMeta.AV_CH_LAYOUT_STEREO,
                    IjkMediaMeta.AV_CH_LAYOUT_2POINT1,
                    IjkMediaMeta.AV_CH_LAYOUT_STEREO_DOWNMIX -> 2

                    IjkMediaMeta.AV_CH_LAYOUT_2_1,
                    IjkMediaMeta.AV_CH_LAYOUT_SURROUND -> 3

                    IjkMediaMeta.AV_CH_LAYOUT_3POINT1,
                    IjkMediaMeta.AV_CH_LAYOUT_4POINT0,
                    IjkMediaMeta.AV_CH_LAYOUT_2_2,
                    IjkMediaMeta.AV_CH_LAYOUT_QUAD -> 4

                    IjkMediaMeta.AV_CH_LAYOUT_4POINT1,
                    IjkMediaMeta.AV_CH_LAYOUT_5POINT0 -> 5

                    IjkMediaMeta.AV_CH_LAYOUT_HEXAGONAL,
                    IjkMediaMeta.AV_CH_LAYOUT_5POINT1,
                    IjkMediaMeta.AV_CH_LAYOUT_6POINT0 -> 6

                    IjkMediaMeta.AV_CH_LAYOUT_6POINT1,
                    IjkMediaMeta.AV_CH_LAYOUT_7POINT0 -> 7

                    IjkMediaMeta.AV_CH_LAYOUT_7POINT1,
                    IjkMediaMeta.AV_CH_LAYOUT_7POINT1_WIDE,
                    IjkMediaMeta.AV_CH_LAYOUT_7POINT1_WIDE_BACK,
                    IjkMediaMeta.AV_CH_LAYOUT_OCTAGONAL -> 8

                    else -> 0
                },
                channelsLabel = when (info.mMeta.mAudioStream?.mChannelLayout) {
                    IjkMediaMeta.AV_CH_LAYOUT_MONO -> "单声道"
                    IjkMediaMeta.AV_CH_LAYOUT_STEREO -> "立体声"
                    IjkMediaMeta.AV_CH_LAYOUT_2POINT1 -> "2.1 声道"
                    IjkMediaMeta.AV_CH_LAYOUT_2_1 -> "立体声"
                    IjkMediaMeta.AV_CH_LAYOUT_SURROUND -> "环绕声"
                    IjkMediaMeta.AV_CH_LAYOUT_3POINT1 -> "3.1 环绕声"
                    IjkMediaMeta.AV_CH_LAYOUT_4POINT0 -> "4.0 四声道"
                    IjkMediaMeta.AV_CH_LAYOUT_4POINT1 -> "4.1 环绕声"
                    IjkMediaMeta.AV_CH_LAYOUT_2_2 -> "四声道"
                    IjkMediaMeta.AV_CH_LAYOUT_QUAD -> "四声道"
                    IjkMediaMeta.AV_CH_LAYOUT_5POINT0 -> "5.0 环绕声"
                    IjkMediaMeta.AV_CH_LAYOUT_5POINT1 -> "5.1 环绕声"
                    IjkMediaMeta.AV_CH_LAYOUT_6POINT0 -> "6.0 环绕声"
                    IjkMediaMeta.AV_CH_LAYOUT_6POINT1 -> "6.1 环绕声"
                    IjkMediaMeta.AV_CH_LAYOUT_7POINT0 -> "7.0 环绕声"
                    IjkMediaMeta.AV_CH_LAYOUT_7POINT1 -> "7.1 环绕声"
                    IjkMediaMeta.AV_CH_LAYOUT_7POINT1_WIDE -> "宽域 7.1 环绕声"
                    IjkMediaMeta.AV_CH_LAYOUT_7POINT1_WIDE_BACK -> "后置 7.1 环绕声"
                    IjkMediaMeta.AV_CH_LAYOUT_HEXAGONAL -> "六角环绕声"
                    IjkMediaMeta.AV_CH_LAYOUT_OCTAGONAL -> "八角环绕声"
                    IjkMediaMeta.AV_CH_LAYOUT_STEREO_DOWNMIX -> "立体声下混音"
                    else -> null
                },
                sampleRate = info.mMeta.mAudioStream?.mSampleRate,
                bitrate = info.mMeta.mAudioStream?.mBitrate?.toInt(),
                mimeType = info.mMeta.mAudioStream?.mCodecName,
                decoder = info.mAudioDecoderImpl,
            ),
        )

        triggerMetadata(metadata)
        triggerReady()
        triggerBuffering(false)
        triggerDuration(player.duration)

        updateJob?.cancel()
        updateJob = coroutineScope.launch {
            while (true) {
                triggerIsPlayingChanged(player.isPlaying)
                triggerCurrentPosition(player.currentPosition)
                delay(500)
            }
        }
    }

    override fun onError(player: IMediaPlayer, what: Int, extra: Int): Boolean {
        // 对于RTSP流的特殊错误处理
        if (::currentChannelLine.isInitialized && currentChannelLine.playableUrl.startsWith("rtsp://", ignoreCase = true)) {
            when (what) {
                IMediaPlayer.MEDIA_ERROR_IO -> {
                    // IO错误，可能是网络问题，尝试重连
                    if (extra == -1004 || extra == -1007) { // 连接超时或网络不可达
                        triggerError(PlaybackException("RTSP_CONNECTION_FAILED", extra))
                    } else {
                        triggerError(PlaybackException("RTSP_IO_ERROR", extra))
                    }
                }
                IMediaPlayer.MEDIA_ERROR_MALFORMED -> {
                    triggerError(PlaybackException("RTSP_MALFORMED_STREAM", extra))
                }
                IMediaPlayer.MEDIA_ERROR_UNSUPPORTED -> {
                    triggerError(PlaybackException("RTSP_UNSUPPORTED_FORMAT", extra))
                }
                IMediaPlayer.MEDIA_ERROR_TIMED_OUT -> {
                    triggerError(PlaybackException("RTSP_TIMEOUT", extra))
                }
                else -> {
                    triggerError(PlaybackException("RTSP_ERROR_WHAT_$what", extra))
                }
            }
        } else {
            triggerError(PlaybackException("IJK_ERROR_WHAT_$what", extra))
        }
        return true
    }

    override fun onVideoSizeChanged(
        player: IMediaPlayer,
        width: Int,
        height: Int,
        sarNum: Int,
        sarDen: Int
    ) {
        triggerResolution(width, height)
    }
}