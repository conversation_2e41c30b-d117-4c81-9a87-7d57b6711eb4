<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.chinablue.tv"
    android:versionCode="2"
    android:versionName="3.3.9" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="35" />

    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />

    <queries>
        <package android:name="com.google.android.webview" />
    </queries>

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <permission
        android:name="com.chinablue.tv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.chinablue.tv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="top.yogiczy.mytv.tv.MyTVApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:banner="@mipmap/tv_banner"
        android:debuggable="true"
        android:extractNativeLibs="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.MyTV"
        android:usesCleartextTraffic="true" >
        <activity
            android:name="top.yogiczy.mytv.tv.MainActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|screenLayout|keyboardHidden"
            android:exported="true"
            android:resizeableActivity="true"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name="top.yogiczy.mytv.tv.CrashHandlerActivity" />

        <receiver
            android:name="top.yogiczy.mytv.tv.BootReceiver"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.RECEIVE_BOOT_COMPLETED" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <service android:name="top.yogiczy.mytv.tv.HttpServerService" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.chinablue.tv.FileProvider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <meta-data
            android:name="io.sentry.auto-init"
            android:value="false" />

        <!-- 'android:authorities' must be unique in the device, across all apps -->
        <provider
            android:name="io.sentry.android.core.SentryInitProvider"
            android:authorities="com.chinablue.tv.SentryInitProvider"
            android:exported="false" />
        <provider
            android:name="io.sentry.android.core.SentryPerformanceProvider"
            android:authorities="com.chinablue.tv.SentryPerformanceProvider"
            android:exported="false"
            android:initOrder="200" />
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.chinablue.tv.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <activity
            android:name="androidx.activity.ComponentActivity"
            android:exported="true" />
        <activity
            android:name="androidx.compose.ui.tooling.PreviewActivity"
            android:exported="true" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>