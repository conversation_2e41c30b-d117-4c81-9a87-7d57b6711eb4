1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.chinablue.tv"
4    android:versionCode="2"
5    android:versionName="3.3.9" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-feature
11-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:5:5-7:36
12        android:name="android.hardware.touchscreen"
12-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:6:9-52
13        android:required="false" />
13-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:7:9-33
14    <uses-feature
14-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:8:5-10:36
15        android:name="android.software.leanback"
15-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:9:9-49
16        android:required="false" />
16-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:10:9-33
17
18    <queries>
18-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:12:5-14:15
19        <package android:name="com.google.android.webview" />
19-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:13:9-62
19-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:13:18-59
20    </queries>
21
22    <uses-permission android:name="android.permission.INTERNET" />
22-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:16:5-67
22-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:16:22-64
23    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
23-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:17:5-81
23-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:17:22-78
24    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
24-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:18:5-83
24-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:18:22-80
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:19:5-80
25-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:19:22-77
26    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
26-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:20:5-22:40
26-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:21:9-66
27    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
27-->[com.github.CarGuo.GSYVideoPlayer:gsyvideoplayer-java:v10.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d59967f9db1cbd94e466e1a40bd990ef\transformed\gsyvideoplayer-java-v10.0.0\AndroidManifest.xml:7:5-79
27-->[com.github.CarGuo.GSYVideoPlayer:gsyvideoplayer-java:v10.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d59967f9db1cbd94e466e1a40bd990ef\transformed\gsyvideoplayer-java-v10.0.0\AndroidManifest.xml:7:22-76
28
29    <permission
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
30        android:name="com.chinablue.tv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
31        android:protectionLevel="signature" />
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
32
33    <uses-permission android:name="com.chinablue.tv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
34
35    <application
35-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:24:5-81:19
36        android:name="top.yogiczy.mytv.tv.MyTVApplication"
36-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:25:9-40
37        android:allowBackup="true"
37-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:26:9-35
38        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
39        android:banner="@mipmap/tv_banner"
39-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:27:9-43
40        android:debuggable="true"
41        android:extractNativeLibs="true"
42        android:icon="@mipmap/ic_launcher"
42-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:28:9-43
43        android:label="@string/app_name"
43-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:29:9-41
44        android:networkSecurityConfig="@xml/network_security_config"
44-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:30:9-69
45        android:requestLegacyExternalStorage="true"
45-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:31:9-52
46        android:supportsRtl="true"
46-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:32:9-35
47        android:testOnly="true"
48        android:theme="@style/Theme.MyTV"
48-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:33:9-42
49        android:usesCleartextTraffic="true" >
49-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:34:9-44
50        <activity
50-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:36:9-50:20
51            android:name="top.yogiczy.mytv.tv.MainActivity"
51-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:37:13-41
52            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|screenLayout|keyboardHidden"
52-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:38:13-119
53            android:exported="true"
53-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:39:13-36
54            android:resizeableActivity="true"
54-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:40:13-46
55            android:screenOrientation="sensorLandscape"
55-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:41:13-56
56            android:supportsPictureInPicture="true" >
56-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:42:13-52
57            <intent-filter>
57-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:44:13-49:29
58                <action android:name="android.intent.action.MAIN" />
58-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:45:17-69
58-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:45:25-66
59
60                <category android:name="android.intent.category.LAUNCHER" />
60-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:47:17-77
60-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:47:27-74
61                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
61-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:48:17-86
61-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:48:27-83
62            </intent-filter>
63        </activity>
64        <activity android:name="top.yogiczy.mytv.tv.CrashHandlerActivity" />
64-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:52:9-58
64-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:52:19-55
65
66        <receiver
66-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:54:9-63:20
67            android:name="top.yogiczy.mytv.tv.BootReceiver"
67-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:55:13-41
68            android:enabled="true"
68-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:56:13-35
69            android:exported="false"
69-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:57:13-37
70            android:permission="android.permission.RECEIVE_BOOT_COMPLETED" >
70-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:58:13-75
71            <intent-filter>
71-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:59:13-62:29
72                <action android:name="android.intent.action.BOOT_COMPLETED" />
72-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:60:17-79
72-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:60:25-76
73
74                <category android:name="android.intent.category.DEFAULT" />
74-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:61:17-76
74-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:61:27-73
75            </intent-filter>
76        </receiver>
77
78        <service android:name="top.yogiczy.mytv.tv.HttpServerService" />
78-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:65:9-54
78-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:65:18-51
79
80        <provider
81            android:name="androidx.core.content.FileProvider"
81-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:68:13-62
82            android:authorities="com.chinablue.tv.FileProvider"
82-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:69:13-64
83            android:exported="false"
83-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:70:13-37
84            android:grantUriPermissions="true" >
84-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:71:13-47
85            <meta-data
85-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:72:13-74:54
86                android:name="android.support.FILE_PROVIDER_PATHS"
86-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:73:17-67
87                android:resource="@xml/file_paths" />
87-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:74:17-51
88        </provider>
89
90        <meta-data
90-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:77:9-79:37
91            android:name="io.sentry.auto-init"
91-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:78:13-47
92            android:value="false" />
92-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:79:13-34
93
94        <!-- 'android:authorities' must be unique in the device, across all apps -->
95        <provider
95-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:12:9-15:40
96            android:name="io.sentry.android.core.SentryInitProvider"
96-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:13:13-69
97            android:authorities="com.chinablue.tv.SentryInitProvider"
97-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:14:13-70
98            android:exported="false" />
98-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:15:13-37
99        <provider
99-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:16:9-20:39
100            android:name="io.sentry.android.core.SentryPerformanceProvider"
100-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:17:13-76
101            android:authorities="com.chinablue.tv.SentryPerformanceProvider"
101-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:18:13-77
102            android:exported="false"
102-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:19:13-37
103            android:initOrder="200" />
103-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:20:13-36
104        <provider
104-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:24:9-32:20
105            android:name="androidx.startup.InitializationProvider"
105-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:25:13-67
106            android:authorities="com.chinablue.tv.androidx-startup"
106-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:26:13-68
107            android:exported="false" >
107-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:27:13-37
108            <meta-data
108-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:29:13-31:52
109                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
109-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:30:17-78
110                android:value="androidx.startup" />
110-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:31:17-49
111            <meta-data
111-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
112                android:name="androidx.emoji2.text.EmojiCompatInitializer"
112-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
113                android:value="androidx.startup" />
113-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
114            <meta-data
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
115                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
116                android:value="androidx.startup" />
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
117        </provider>
118
119        <activity
119-->[androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:23:9-25:39
120            android:name="androidx.activity.ComponentActivity"
120-->[androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:24:13-63
121            android:exported="true" />
121-->[androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:25:13-36
122        <activity
122-->[androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
123            android:name="androidx.compose.ui.tooling.PreviewActivity"
123-->[androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
124            android:exported="true" />
124-->[androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
125
126        <receiver
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
127            android:name="androidx.profileinstaller.ProfileInstallReceiver"
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
128            android:directBootAware="false"
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
129            android:enabled="true"
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
130            android:exported="true"
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
131            android:permission="android.permission.DUMP" >
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
132            <intent-filter>
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
133                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
134            </intent-filter>
135            <intent-filter>
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
136                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
137            </intent-filter>
138            <intent-filter>
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
139                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
140            </intent-filter>
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
142                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
143            </intent-filter>
144        </receiver>
145    </application>
146
147</manifest>
