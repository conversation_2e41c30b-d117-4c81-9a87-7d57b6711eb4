<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res"><file name="mm_reward_qrcode" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\drawable\mm_reward_qrcode.png" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="tv_banner" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\mipmap-hdpi\tv_banner.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="tv_banner" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\mipmap-mdpi\tv_banner.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="tv_banner" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\mipmap-xhdpi\tv_banner.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="tv_banner" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\mipmap-xxhdpi\tv_banner.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="tv_banner" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\mipmap-xxxhdpi\tv_banner.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="app_themes" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\raw\app_themes.json" qualifiers="" type="raw"/><file name="web_push" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\raw\web_push.html" qualifiers="" type="raw"/><file name="web_push_css" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\raw\web_push_css.css" qualifiers="" type="raw"/><file name="web_push_js" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\raw\web_push_js.js" qualifiers="" type="raw"/><file path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">天光云影</string></file><file path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.MyTV" parent="android:Theme.Black.NoTitleBar.Fullscreen"/></file><file name="file_paths" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="disguised$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\disguised\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="disguised" generated-set="disguised$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\disguised\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\tv\build\generated\res\resValues\disguised\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\tv\build\generated\res\resValues\disguised\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\disguisedDebug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant" generated-set="variant$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\disguisedDebug\res"/></dataSet><mergedItems/></merger>