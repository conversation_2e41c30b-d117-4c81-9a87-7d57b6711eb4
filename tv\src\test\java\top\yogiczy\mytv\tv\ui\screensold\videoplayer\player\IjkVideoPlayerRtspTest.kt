package top.yogiczy.mytv.tv.ui.screensold.videoplayer.player

import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mockito.*
import top.yogiczy.mytv.core.data.entities.channel.ChannelLine
import tv.danmaku.ijk.media.player.IjkMediaPlayer

/**
 * IJK播放器RTSP播放测试
 * 测试RTSP流播放时的配置和错误处理
 */
class IjkVideoPlayerRtspTest {

    @Test
    fun `test RTSP URL detection`() {
        val rtspUrl = "rtsp://example.com:554/stream"
        val httpUrl = "http://example.com/stream.m3u8"
        
        assertTrue("RTSP URL should be detected", rtspUrl.startsWith("rtsp://", ignoreCase = true))
        assertFalse("HTTP URL should not be detected as RTSP", httpUrl.startsWith("rtsp://", ignoreCase = true))
    }

    @Test
    fun `test RTSP configuration options`() {
        // 验证RTSP相关的配置选项是否正确设置
        val expectedOptions = mapOf(
            "rtsp_transport" to "tcp",
            "rtsp_flags" to "prefer_tcp",
            "stimeout" to 20000000L,
            "max_delay" to 500000L,
            "buffer_size" to 1024 * 1024,
            "infbuf" to 1,
            "packet-buffering" to 0,
            "sync" to "ext",
            "max_cached_duration" to 3000L,
            "min-frames" to 2,
            "max-fps" to 0
        )
        
        // 这些配置应该能够解决RTSP播放时画面卡住的问题
        assertNotNull("RTSP transport should be set to TCP", expectedOptions["rtsp_transport"])
        assertEquals("Buffer size should be 1MB", 1024 * 1024, expectedOptions["buffer_size"])
        assertEquals("Max cached duration should be 3 seconds", 3000L, expectedOptions["max_cached_duration"])
    }

    @Test
    fun `test RTSP error codes`() {
        val errorCodes = mapOf(
            "RTSP_CONNECTION_FAILED" to "RTSP连接失败，请检查网络连接和服务器状态。",
            "RTSP_IO_ERROR" to "RTSP流读取错误，可能是网络不稳定。",
            "RTSP_MALFORMED_STREAM" to "RTSP流格式错误，请检查流媒体服务器配置。",
            "RTSP_UNSUPPORTED_FORMAT" to "不支持的RTSP流格式，请尝试其他播放器。",
            "RTSP_TIMEOUT" to "RTSP连接超时，请检查网络连接。"
        )
        
        errorCodes.forEach { (code, description) ->
            assertNotNull("Error code $code should have description", description)
            assertTrue("Description should be in Chinese", description.contains("RTSP") || description.contains("连接") || description.contains("格式"))
        }
    }

    @Test
    fun `test channel line with RTSP URL`() {
        val rtspChannelLine = ChannelLine(
            playableUrl = "rtsp://*************:554/live/stream1",
            httpUserAgent = "MyTV/1.0"
        )
        
        assertTrue("Should detect RTSP URL", rtspChannelLine.playableUrl.startsWith("rtsp://"))
        assertNotNull("User agent should be set", rtspChannelLine.httpUserAgent)
    }

    @Test
    fun `test framedrop configuration for RTSP`() {
        // 验证帧丢弃配置从5改为1，这应该减少画面卡住的问题
        val originalFramedrop = 5
        val optimizedFramedrop = 1
        
        assertTrue("Optimized framedrop should be less than original", optimizedFramedrop < originalFramedrop)
        assertEquals("Framedrop should be set to 1 for RTSP", 1, optimizedFramedrop)
    }
}
