{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeDisguisedDebugResources-75:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5a79ec61513eb95e1266e907126a2152\\transformed\\media3-ui-1.4.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,313,512,696,779,864,943,1036,1128,1205,1268,1360,1447,1510,1572,1633,1700,1816,1937,2057,2126,2202,2272,2344,2429,2516,2579,2653,2707,2776,2824,2885,2943,3020,3084,3148,3208,3270,3335,3387,3446,3519,3592,3645", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,82,84,78,92,91,76,62,91,86,62,61,60,66,115,120,119,68,75,69,71,84,86,62,73,53,68,47,60,57,76,63,63,59,61,64,51,58,72,72,52,64", "endOffsets": "308,507,691,774,859,938,1031,1123,1200,1263,1355,1442,1505,1567,1628,1695,1811,1932,2052,2121,2197,2267,2339,2424,2511,2574,2648,2702,2771,2819,2880,2938,3015,3079,3143,3203,3265,3330,3382,3441,3514,3587,3640,3705"}, "to": {"startLines": "2,11,15,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,363,562,4604,4687,4772,4851,4944,5036,5113,5176,5268,5355,5418,5480,5541,5608,5724,5845,5965,6034,6110,6180,6252,6337,6424,6487,7211,7265,7334,7382,7443,7501,7578,7642,7706,7766,7828,7893,7945,8004,8077,8150,8203", "endLines": "10,14,18,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,82,84,78,92,91,76,62,91,86,62,61,60,66,115,120,119,68,75,69,71,84,86,62,73,53,68,47,60,57,76,63,63,59,61,64,51,58,72,72,52,64", "endOffsets": "358,557,741,4682,4767,4846,4939,5031,5108,5171,5263,5350,5413,5475,5536,5603,5719,5840,5960,6029,6105,6175,6247,6332,6419,6482,6556,7260,7329,7377,7438,7496,7573,7637,7701,7761,7823,7888,7940,7999,8072,8145,8198,8263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c98e1a41ddbd0f58bbf4b7505ad9616c\\transformed\\appcompat-1.7.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "746,856,957,1068,1152,1253,1368,1448,1525,1618,1713,1805,1899,2001,2096,2193,2287,2380,2470,2552,2660,2764,2862,2968,3073,3178,3335,8784", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "851,952,1063,1147,1248,1363,1443,1520,1613,1708,1800,1894,1996,2091,2188,2282,2375,2465,2547,2655,2759,2857,2963,3068,3173,3330,3431,8861"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f0ce41bc1ab5a7ccfdd795c249b2c5b2\\transformed\\ui-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,192,271,365,463,549,631,734,819,902,983,1065,1139,1214,1288,1360,1435,1502", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,74,73,71,74,66,116", "endOffsets": "187,266,360,458,544,626,729,814,897,978,1060,1134,1209,1283,1355,1430,1497,1614"}, "to": {"startLines": "53,54,55,56,57,108,109,110,111,112,113,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4160,4247,4326,4420,4518,8268,8350,8453,8538,8621,8702,8866,8940,9015,9089,9262,9337,9404", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,74,73,71,74,66,116", "endOffsets": "4242,4321,4415,4513,4599,8345,8448,8533,8616,8697,8779,8935,9010,9084,9156,9332,9399,9516"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9db5af2f2458547656275ea3d5385dab\\transformed\\media3-exoplayer-1.4.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,183,250,310,388,463,552,640", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "117,178,245,305,383,458,547,635,700"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6561,6628,6689,6756,6816,6894,6969,7058,7146", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "6623,6684,6751,6811,6889,6964,7053,7141,7206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd2de9df1fcb7d47d0f664953b17c729\\transformed\\foundation-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,88", "endOffsets": "139,228"}, "to": {"startLines": "123,124", "startColumns": "4,4", "startOffsets": "9521,9610", "endColumns": "88,88", "endOffsets": "9605,9694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\55d105b608835fb0a5975933fd0070b6\\transformed\\core-1.13.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "46,47,48,49,50,51,52,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3436,3535,3637,3736,3836,3937,4043,9161", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "3530,3632,3731,3831,3932,4038,4155,9257"}}]}]}