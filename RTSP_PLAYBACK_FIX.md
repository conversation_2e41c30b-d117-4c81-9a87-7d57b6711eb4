# RTSP播放画面卡住问题修复

## 问题描述
IJK播放器播放RTSP流时，会出现播放第一帧画面后画面完全卡住不动的问题，但网络流量仍在使用，说明数据在传输但视频渲染出现问题。

## 问题分析
1. **缓冲配置不当**: 原有配置可能导致缓冲区积压，影响实时播放
2. **帧丢弃策略**: `framedrop=5` 设置过高，可能导致关键帧被丢弃
3. **RTSP协议配置**: 缺少RTSP特定的传输和缓冲配置
4. **同步机制**: 音视频同步配置不适合实时流播放

## 修复方案

### 1. RTSP特定格式配置
在播放器初始化时添加RTSP专用配置：

```kotlin
// RTSP 特定配置
setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "rtsp_transport", "tcp")
setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "rtsp_flags", "prefer_tcp")
setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "stimeout", 20000000L) // 20秒超时
setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "max_delay", 500000L) // 最大延迟500ms
setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "buffer_size", 1024 * 1024) // 1MB缓冲区
setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "infbuf", 1) // 无限缓冲
```

### 2. 播放器配置优化
调整播放器参数以适应实时流播放：

```kotlin
// 针对RTSP流的播放器配置
player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "framedrop", 1) // 减少丢帧
player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "packet-buffering", 0) // 禁用包缓冲
player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "sync", "ext") // 外部同步
player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max_cached_duration", 3000L) // 最大缓存3秒
player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "min-frames", 2) // 最小帧数
player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max-fps", 0) // 不限制帧率
```

### 3. RTSP流特殊处理
针对RTSP URL进行特殊处理：

```kotlin
// 针对RTSP流的特殊处理
if (line.playableUrl.startsWith("rtsp://", ignoreCase = true)) {
    // RTSP流不需要HTTP headers
    player.setDataSource(line.playableUrl)
    // 确保立即开始播放
    player.start()
}
```

### 4. 错误处理增强
添加RTSP特定的错误处理和用户友好的错误信息：

```kotlin
when (what) {
    IMediaPlayer.MEDIA_ERROR_IO -> {
        if (extra == -1004 || extra == -1007) {
            triggerError(PlaybackException("RTSP_CONNECTION_FAILED", extra))
        }
    }
    IMediaPlayer.MEDIA_ERROR_TIMED_OUT -> {
        triggerError(PlaybackException("RTSP_TIMEOUT", extra))
    }
    // ... 其他错误类型
}
```

## 关键配置说明

### rtsp_transport = "tcp"
- 强制使用TCP传输RTSP数据
- 避免UDP丢包导致的画面卡顿

### framedrop = 1
- 从原来的5降低到1
- 减少关键帧被丢弃的可能性

### packet-buffering = 0
- 禁用包缓冲
- 减少延迟，提高实时性

### max_cached_duration = 3000L
- 限制最大缓存时长为3秒
- 防止缓冲区积压过多数据

### infbuf = 1
- 启用无限缓冲
- 允许播放器根据需要调整缓冲区大小

## 测试验证
创建了专门的测试文件 `IjkVideoPlayerRtspTest.kt` 来验证：
1. RTSP URL检测逻辑
2. 配置参数正确性
3. 错误处理机制
4. 帧丢弃优化效果

## 预期效果
1. **解决画面卡住**: 通过优化缓冲和同步机制
2. **提高播放稳定性**: TCP传输和错误重试机制
3. **减少延迟**: 降低缓冲时长和禁用不必要的缓冲
4. **更好的错误提示**: 用户友好的RTSP错误信息

## 使用建议
1. 确保网络连接稳定
2. 如果仍有问题，可以尝试调整 `max_cached_duration` 参数
3. 对于特别不稳定的网络，可以适当增加 `stimeout` 值
4. 监控播放器日志以便进一步调试
